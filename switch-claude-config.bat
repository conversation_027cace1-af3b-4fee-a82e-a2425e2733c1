@echo off
setlocal EnableDelayedExpansion

title Claude Config Switcher

set "claude_dir=%USERPROFILE%\.claude"
set "current_index=0"
set "file_count=0"

echo Scanning for Claude configuration files...

if not exist "%claude_dir%" (
    echo Error: .claude directory not found in %USERPROFILE%
    pause
    exit /b 1
)

cd /d "%claude_dir%"

for %%f in (settings-*.json) do (
    set "files[!file_count!]=%%f"
    set /a file_count+=1
)

if !file_count! equ 0 (
    echo No settings-*.json files found in %claude_dir%
    pause
    exit /b 1
)

set /a max_index=!file_count!-1

rem Display current settings.json content
if exist "settings.json" (
    echo.
    echo ======================================
    echo     Current settings.json Content
    echo ======================================
    echo.
    type "settings.json"
    echo.
    echo ======================================
    echo.
    pause
) else (
    echo.
    echo No active settings.json file found.
    echo.
    pause
)

:display_menu
cls
echo.
echo ======================================
echo        Claude Config Switcher
echo ======================================
echo.
echo Use UP/DOWN arrows to navigate, ENTER to select, P to preview, ESC to exit
echo.

for /l %%i in (0,1,!max_index!) do (
    if %%i equ !current_index! (
        echo ^> !files[%%i]!
    ) else (
        echo   !files[%%i]!
    )
)

echo.
echo Currently active: settings.json

:get_input
for /f "delims=" %%k in ('powershell -command "$key = $host.ui.rawui.readkey('NoEcho,IncludeKeyDown'); $key.VirtualKeyCode"') do set key=%%k

if !key! equ 38 (
    if !current_index! gtr 0 (
        set /a current_index-=1
    ) else (
        set current_index=!max_index!
    )
    goto display_menu
)

if !key! equ 40 (
    if !current_index! lss !max_index! (
        set /a current_index+=1
    ) else (
        set current_index=0
    )
    goto display_menu
)

if !key! equ 13 (
    goto copy_file
)

if !key! equ 80 (
    goto preview_file
)

if !key! equ 112 (
    goto preview_file
)

if !key! equ 27 (
    echo.
    echo Operation cancelled.
    pause
    exit /b 0
)

goto get_input

:preview_file
cls
echo.
echo ======================================
echo   Preview: !files[%current_index%]!
echo ======================================
echo.
if exist "!files[%current_index%]!" (
    type "!files[%current_index%]!"
) else (
    echo Error: File not found.
)
echo.
echo ======================================
echo Press any key to return to menu...
pause >nul
goto display_menu

:copy_file
cls
echo.
echo Selected: !files[%current_index%]!
echo.

if exist "settings.json" (
    echo Removing existing settings.json...
    del "settings.json"
    if exist "settings.json" (
        echo Error: Could not delete existing settings.json
        pause
        exit /b 1
    )
)

echo Copying !files[%current_index%]! to settings.json...
copy "!files[%current_index%]!" "settings.json" >nul

if exist "settings.json" (
    echo.
    echo Configuration switched successfully!
    echo Active config: !files[%current_index%]!
) else (
    echo.
    echo Error: Failed to copy configuration file.
)

echo.
pause